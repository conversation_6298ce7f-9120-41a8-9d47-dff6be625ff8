# Services package
"""
Optimized services package for ChartFix Discord Bot

Core Services:
- market_service: Market data, prices, and watchlist functionality
- portfolio_service: Portfolio management and trading
- database_service: Database operations
- discord_service: Discord utilities and formatting
- error_service: Error handling and logging
- symbol_service: Symbol normalization and validation
- cache_service: Caching with TTL support
- market_monitor_service: Market monitoring and alerts
- trading_time_service: Trading time analysis and optimal timing alerts
- chart_service: Chart generation and technical analysis
- news_service: News aggregation and market reports
- http_client_service: HTTP client management
"""

# Core service imports
from .market.market_service import get_market_service
from .core.portfolio_service import get_portfolio_service
from .data.database_service import DatabaseService
from .data.cache_service import get_cache_service
from .market.market_monitor_service import get_market_monitor_service
from .market.trading_time_service import get_trading_time_service
from .market.chart_service import get_chart_service
from .market.news_service import get_news_service
from .core.http_client_service import get_http_client

__all__ = [
    'get_market_service',
    'get_portfolio_service',
    'DatabaseService',
    'get_cache_service',
    'get_market_monitor_service',
    'get_trading_time_service',
    'get_chart_service',
    'get_news_service',
    'get_http_client'
]
