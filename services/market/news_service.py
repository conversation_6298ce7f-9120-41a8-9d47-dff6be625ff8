"""
News Service for Market News System
Handles news aggregation from multiple sources
"""

import logging
import asyncio
import threading
from typing import Dict, List, Any
from datetime import datetime, timezone, timedelta

from services.core.error_service import handle_service_errors, retry_with_backoff
from services.data.cache_service import get_cache_service
from services.core.http_client_service import get_http_client
from services.market.market_service import get_market_service
from utils.config import load_config

from utils.constants import EMOJI, MESSAGE_TEMPLATES

logger = logging.getLogger(__name__)

def _format_percentage_enhanced(percentage: float) -> str:
    """Local implementation to avoid circular import"""
    if percentage > 0:
        return f"🟢+{percentage:.2f}%"
    elif percentage < 0:
        return f"🔴{percentage:.2f}%"
    else:
        return f"⚪{percentage:.2f}%"

class NewsService:
    def __init__(self):
        self.cache_service = get_cache_service()
        self.market_service = get_market_service()

        config = load_config()
        api_config = config.get('market_news_apis', {})
        self.newsapi_key = api_config.get('newsapi_key', '')
        self.alpha_vantage_key = api_config.get('alpha_vantage_key', '')

        self.newsapi_base_url = "https://newsapi.org/v2"
        self.alpha_vantage_base_url = "https://www.alphavantage.co/query"

        self.news_cache_ttl = 3600
        self.max_articles_per_source = 5

        # Economic calendar configuration
        calendar_config = config.get('economic_calendar', {})
        self.forex_factory_base_url = calendar_config.get('forex_factory_base_url', 'https://nfs.faireconomy.media')
        self.calendar_cache_ttl = calendar_config.get('cache_ttl', 1800)
        self.max_events_display = calendar_config.get('max_events_display', 10)
        self.impact_filter = calendar_config.get('impact_filter', ['High', 'Medium'])
        self.currency_filter = calendar_config.get('currency_filter', ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY'])
        self.time_range_hours = calendar_config.get('time_range_hours', 24)

        # Market report configuration (from MarketNewsService)
        self.max_news_items = 5
        self.report_title = MESSAGE_TEMPLATES['market_report']['title'].format(
            newspaper=EMOJI['newspaper'],
            globe=EMOJI['globe']
        )

        logger.info("News Service initialized")

    @handle_service_errors
    @retry_with_backoff(max_retries=2)
    async def fetch_crypto_news(self) -> List[Dict[str, Any]]:
        cache_key = "crypto_news"

        cached_news = self.cache_service.get(cache_key)
        if cached_news:
            logger.info("Returning cached crypto news")
            return cached_news

        try:
            http_client = await get_http_client()
            url = f"{self.newsapi_base_url}/everything"
            params = {
                "q": "cryptocurrency OR bitcoin OR ethereum OR crypto OR blockchain",
                "language": "en",
                "sortBy": "publishedAt",
                "pageSize": self.max_articles_per_source,
                "apiKey": self.newsapi_key
            }

            async with http_client.request('GET', url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    articles = data.get('articles', [])

                    processed_articles = []
                    for article in articles:
                        processed_article = {
                            'title': article.get('title', 'No title'),
                            'description': article.get('description', 'No description'),
                            'url': article.get('url', ''),
                            'source': article.get('source', {}).get('name', 'Unknown'),
                            'published_at': article.get('publishedAt', ''),
                            'category': 'crypto'
                        }
                        processed_articles.append(processed_article)

                    self.cache_service.set(cache_key, processed_articles, ttl=self.news_cache_ttl)
                    logger.info(f"Fetched {len(processed_articles)} crypto news articles")
                    return processed_articles
                else:
                    logger.error(f"NewsAPI crypto error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error fetching crypto news: {e}")
            return []

    @handle_service_errors
    @retry_with_backoff(max_retries=2)
    async def fetch_financial_news(self) -> List[Dict[str, Any]]:
        """Fetch financial news from NewsAPI"""
        cache_key = "financial_news"

        # Check cache
        cached_news = self.cache_service.get(cache_key)
        if cached_news:
            logger.info("Returning cached financial news")
            return cached_news

        try:
            http_client = await get_http_client()
            url = f"{self.newsapi_base_url}/top-headlines"
            params = {
                "category": "business",
                "language": "en",
                "pageSize": self.max_articles_per_source,
                "apiKey": self.newsapi_key
            }

            async with http_client.request('GET', url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    articles = data.get('articles', [])

                    # Process articles
                    processed_articles = []
                    for article in articles:
                        processed_article = {
                            'title': article.get('title', 'No title'),
                            'description': article.get('description', 'No description'),
                            'url': article.get('url', ''),
                            'source': article.get('source', {}).get('name', 'Unknown'),
                            'published_at': article.get('publishedAt', ''),
                            'category': 'financial'
                        }
                        processed_articles.append(processed_article)

                    # Cache results
                    self.cache_service.set(cache_key, processed_articles, ttl=self.news_cache_ttl)
                    logger.info(f"Fetched {len(processed_articles)} financial news articles")
                    return processed_articles
                else:
                    logger.error(f"NewsAPI financial error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error fetching financial news: {e}")
            return []

    @handle_service_errors
    @retry_with_backoff(max_retries=2)
    async def fetch_global_markets_data(self) -> Dict[str, Any]:
        """Fetch global market indices from Alpha Vantage"""
        cache_key = "global_markets_data"

        # Check cache
        cached_data = self.cache_service.get(cache_key)
        if cached_data:
            logger.info("Returning cached global markets data")
            return cached_data

        # Hardcoded global indices
        indices = {
            "SPY": {"name": "S&P 500", "flag": "🇺🇸", "region": "US"},
            "EWJ": {"name": "Japan", "flag": "🇯🇵", "region": "Asia"},
            "FXI": {"name": "China", "flag": "🇨🇳", "region": "Asia"},
            "VGK": {"name": "Europe", "flag": "🇪🇺", "region": "Europe"},
            "EWG": {"name": "Germany", "flag": "🇩🇪", "region": "Europe"},
            "GLD": {"name": "Gold", "flag": "🥇", "region": "Commodity"}
        }

        results = {}

        try:
            http_client = await get_http_client()
            for symbol, info in indices.items():
                try:
                    url = self.alpha_vantage_base_url
                    params = {
                        "function": "GLOBAL_QUOTE",
                        "symbol": symbol,
                        "apikey": self.alpha_vantage_key
                    }

                    async with http_client.request('GET', url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()

                            if "Global Quote" in data:
                                quote = data["Global Quote"]
                                price = float(quote.get("05. price", 0))
                                change_percent = quote.get("10. change percent", "0%").replace("%", "")
                                change_percent = float(change_percent) if change_percent else 0

                                results[symbol] = {
                                    "name": info["name"],
                                    "flag": info["flag"],
                                    "region": info["region"],
                                    "price": price,
                                    "change_percent": change_percent,
                                    "success": True
                                }
                                logger.debug(f"Fetched {info['name']}: ${price:.2f} ({change_percent:+.2f}%)")
                            else:
                                logger.warning(f"No data for {symbol}")
                                results[symbol] = {"success": False, "error": "No data"}
                        else:
                            logger.warning(f"HTTP {response.status} for {symbol}")
                            results[symbol] = {"success": False, "error": f"HTTP {response.status}"}

                    # Small delay to avoid rate limiting
                    await asyncio.sleep(0.3)

                except Exception as e:
                    logger.error(f"Error fetching {symbol}: {e}")
                    results[symbol] = {"success": False, "error": str(e)}

            # Cache results
            self.cache_service.set(cache_key, results, ttl=self.news_cache_ttl)
            successful_count = sum(1 for r in results.values() if r.get("success", False))
            logger.info(f"Fetched global markets data: {successful_count}/{len(indices)} successful")
            return results

        except Exception as e:
            logger.error(f"Error fetching global markets data: {e}")
            return {}

    @handle_service_errors
    @retry_with_backoff(max_retries=2)
    async def fetch_economic_calendar(self) -> List[Dict[str, Any]]:
        """Fetch economic calendar events from Forex Factory"""
        cache_key = "economic_calendar"

        cached_data = self.cache_service.get(cache_key)
        if cached_data:
            logger.info("Returning cached economic calendar data")
            return cached_data

        try:
            url = f"{self.forex_factory_base_url}/ff_calendar_thisweek.json"

            http_client = await get_http_client()
            async with http_client.request('GET', url, timeout=15) as response:
                if response.status == 200:
                    events = await response.json()

                    # Filter events
                    filtered_events = self._filter_calendar_events(events)

                    # Cache results
                    self.cache_service.set(cache_key, filtered_events, ttl=self.calendar_cache_ttl)
                    logger.info(f"Fetched {len(filtered_events)} economic calendar events")
                    return filtered_events
                else:
                    logger.error(f"Forex Factory API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error fetching economic calendar: {e}")
            return []

    def _filter_calendar_events(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter and sort economic calendar events"""
        try:
            now = datetime.now(timezone.utc)
            cutoff = now + timedelta(hours=self.time_range_hours)

            filtered = []
            for event in events:
                # Filter by impact
                if event.get('impact') not in self.impact_filter:
                    continue

                # Filter by currency
                if event.get('country') not in self.currency_filter:
                    continue

                # Filter by time range
                try:
                    event_time = datetime.fromisoformat(event['date'].replace('Z', '+00:00'))
                    if not (now <= event_time <= cutoff):
                        continue
                except:
                    continue

                filtered.append(event)

            # Sort by date
            filtered.sort(key=lambda x: x.get('date', ''))

            # Limit results
            return filtered[:self.max_events_display]

        except Exception as e:
            logger.error(f"Error filtering calendar events: {e}")
            return events[:self.max_events_display] if events else []

    async def get_all_news_data(self) -> Dict[str, Any]:
        """Get all news and market data for daily report"""
        try:
            # Fetch all data concurrently
            crypto_news_task = asyncio.create_task(self.fetch_crypto_news())
            financial_news_task = asyncio.create_task(self.fetch_financial_news())
            global_markets_task = asyncio.create_task(self.fetch_global_markets_data())

            # Wait for all tasks to complete
            crypto_news, financial_news, global_markets = await asyncio.gather(
                crypto_news_task, financial_news_task, global_markets_task
            )

            return {
                "crypto_news": crypto_news,
                "financial_news": financial_news,
                "global_markets": global_markets,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "success": True
            }

        except Exception as e:
            logger.error(f"Error getting all news data: {e}")
            return {
                "crypto_news": [],
                "financial_news": [],
                "global_markets": {},
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "success": False,
                "error": str(e)
            }

    @handle_service_errors
    async def generate_daily_report(self) -> Dict[str, Any]:
        """Generate daily market report with formatted sections"""
        try:
            logger.info("Generating daily market report...")

            news_data = await self.get_all_news_data()
            market_overview = await self.market_service.fetch_market_overview()
            trending_data = await self._get_trending_coins()

            report = {
                "title": self.report_title,
                "timestamp": datetime.now(timezone.utc),
                "sections": {
                    "crypto_overview": self._format_crypto_overview(market_overview),
                    "global_markets": self._format_global_markets(news_data.get("global_markets", {})),
                    "trending_coins": self._format_trending_coins(trending_data),
                    "market_sentiment": self._format_market_sentiment(market_overview),
                    "crypto_news": self._format_news_section(news_data.get("crypto_news", []), "🔥 CRYPTO NEWS"),
                    "financial_news": self._format_news_section(news_data.get("financial_news", []), "💼 FINANCIAL NEWS")
                },
                "success": True
            }

            logger.info("Daily market report generated successfully")
            return report

        except Exception as e:
            logger.error(f"Error generating daily report: {e}")
            return {
                "title": "❌ MARKET REPORT ERROR",
                "timestamp": datetime.now(timezone.utc),
                "sections": {},
                "success": False,
                "error": str(e)
            }

    def _format_crypto_overview(self, market_overview: Dict[str, Any]) -> str:
        """Format cryptocurrency market overview section"""
        try:
            market_data = market_overview.get('market_data', {})
            global_data = market_data.get('global', {})

            if not global_data:
                return "❌ Không thể lấy dữ liệu thị trường crypto"

            # Extract data
            total_market_cap = global_data.get('total_market_cap', 0)
            total_volume = global_data.get('total_volume_24h', 0)
            market_cap_change = global_data.get('market_cap_change_24h', 0)
            dominance = global_data.get('dominance', {})

            # Format numbers
            market_cap_str = f"${total_market_cap/1e12:.2f}T" if total_market_cap > 1e12 else f"${total_market_cap/1e9:.1f}B"
            volume_str = f"${total_volume/1e9:.1f}B" if total_volume > 1e9 else f"${total_volume/1e6:.0f}M"
            change_str = _format_percentage_enhanced(market_cap_change)

            section = f"**📊 CRYPTO MARKET OVERVIEW**\n"
            section += f"Market Cap: **{market_cap_str}** {change_str}\n"
            section += f"24h Volume: **{volume_str}**\n"
            section += f"BTC Dominance: **{dominance.get('BTC', 0):.1f}%**\n"
            section += f"ETH Dominance: **{dominance.get('ETH', 0):.1f}%**"

            return section

        except Exception as e:
            logger.error(f"Error formatting crypto overview: {e}")
            return "❌ Lỗi định dạng crypto overview"

    def _format_global_markets(self, global_markets: Dict[str, Any]) -> str:
        """Format global markets section"""
        try:
            if not global_markets:
                return "⚠️ Global markets data unavailable"

            section = "```\n"
            section += "🌍 GLOBAL MARKETS\n"
            section += "══════════════════════════════\n"

            # Order for display
            display_order = ["SPY", "EWJ", "FXI", "VGK", "EWG", "GLD"]

            for symbol in display_order:
                if symbol in global_markets and global_markets[symbol].get("success", False):
                    data = global_markets[symbol]
                    flag = data.get("flag", "📊")
                    name = data.get("name", symbol)
                    price = data.get("price", 0)
                    change = data.get("change_percent", 0)

                    # Format change with enhanced formatting
                    change_str = _format_percentage_enhanced(change)

                    # Format price
                    if price >= 1000:
                        price_str = f"${price:,.0f}"
                    elif price >= 100:
                        price_str = f"${price:.2f}"
                    else:
                        price_str = f"${price:.2f}"

                    section += f"{flag} {name:<8} {price_str:>10} {change_str}\n"

            section += "```"
            return section

        except Exception as e:
            logger.error(f"Error formatting global markets: {e}")
            return "⚠️ Global markets data unavailable (API quota exceeded)"

    async def _get_trending_coins(self) -> List[Dict[str, Any]]:
        """Get trending coins from CoinGecko"""
        try:
            http_client = await get_http_client()
            url = "https://api.coingecko.com/api/v3/search/trending"
            async with http_client.request('GET', url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('coins', [])[:5]  # Top 5
                else:
                    logger.error(f"Error fetching trending coins: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error getting trending coins: {e}")
            return []

    def _format_trending_coins(self, trending_data: List[Dict[str, Any]]) -> str:
        """Format trending coins section"""
        try:
            if not trending_data:
                return "**🔥 TRENDING COINS**\n❌ Không có dữ liệu trending"

            section = "**🔥 TRENDING COINS**\n"

            for i, coin_data in enumerate(trending_data[:5], 1):
                coin = coin_data.get('item', {})
                name = coin.get('name', 'Unknown')
                symbol = coin.get('symbol', 'N/A').upper()
                rank = coin.get('market_cap_rank', 'N/A')

                section += f"{i}. **{name}** ({symbol}) - Rank #{rank}\n"

            return section

        except Exception as e:
            logger.error(f"Error formatting trending coins: {e}")
            return "**🔥 TRENDING COINS**\n❌ Lỗi định dạng trending coins"

    def _format_market_sentiment(self, market_overview: Dict[str, Any]) -> str:
        """Format market sentiment section"""
        try:
            fear_greed = market_overview.get('fear_greed', {})

            if not fear_greed or fear_greed.get('value', 0) == 0:
                return "**😨 MARKET SENTIMENT**\n❌ Không có dữ liệu Fear & Greed"

            value = fear_greed.get('value', 0)
            classification = fear_greed.get('classification', 'Unknown')

            # Determine emoji based on value
            if value >= 75:
                emoji = "🤑"  # Extreme Greed
            elif value >= 55:
                emoji = "😊"  # Greed
            elif value >= 45:
                emoji = "😐"  # Neutral
            elif value >= 25:
                emoji = "😰"  # Fear
            else:
                emoji = "😱"  # Extreme Fear

            section = f"**😨 MARKET SENTIMENT**\n"
            section += f"{emoji} **{classification}** ({value}/100)"

            return section

        except Exception as e:
            logger.error(f"Error formatting market sentiment: {e}")
            return "❌ Lỗi định dạng market sentiment"

    def _format_news_section(self, news_articles: List[Dict[str, Any]], title: str) -> str:
        """Format news section"""
        try:
            if not news_articles:
                return f"{title}\n❌ Không có tin tức"

            section = f"**{title}**\n"

            for i, article in enumerate(news_articles[:self.max_news_items], 1):
                title_text = article.get('title', 'No title')
                source = article.get('source', 'Unknown')

                # Truncate long titles
                if len(title_text) > 80:
                    title_text = title_text[:77] + "..."

                section += f"{i}. **{title_text}** _{source}_\n"

            return section

        except Exception as e:
            logger.error(f"Error formatting news section: {e}")
            return f"{title}\n❌ Lỗi định dạng tin tức"

# Singleton instance
_news_service_instance = None
_instance_lock = threading.RLock()

def get_news_service() -> NewsService:
    """Get singleton instance of NewsService"""
    global _news_service_instance
    with _instance_lock:
        if _news_service_instance is None:
            _news_service_instance = NewsService()
        return _news_service_instance
