2025-06-18 11:49:43,999 - __main__ - WARNING - Slow command execution: watchlist took 6.10s
2025-06-18 12:06:07,817 - handlers.discord.market.market_commands - WARNING - Interaction already expired for market command by fantastic_beagle_96376
2025-06-18 15:30:54,244 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d4f0821c576a93914d8440c65f7a16a7f0252d3ac25b8b2f1bdb7b5c487450cd
2025-06-18 15:30:54,247 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d4f0821c576a93914d8440c65f7a16a7f0252d3ac25b8b2f1bdb7b5c487450cd (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d4f0821c576a93914d8440c65f7a16a7f0252d3ac25b8b2f1bdb7b5c487450cd)
2025-06-18 16:39:04,683 - __main__ - ERROR - Error updating watchlist in channel 1376101742772748328: 503 Service Unavailable (error code: 0): upstream connect error or disconnect/reset before headers. reset reason: remote connection failure, transport failure reason: immediate connect error: No such file or directory
2025-06-18 22:22:11,314 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ea82e927992e5f8151a8130d737cfbb3e2e1c3cf6111ad305880105b31c3c8ea
2025-06-18 22:22:11,318 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ea82e927992e5f8151a8130d737cfbb3e2e1c3cf6111ad305880105b31c3c8ea (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ea82e927992e5f8151a8130d737cfbb3e2e1c3cf6111ad305880105b31c3c8ea)
2025-06-18 22:25:22,261 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=054603b38759f5b78891bcabd5b5e78e89207d5f3603246e2fd2deaae04a058e
2025-06-18 22:25:22,263 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=054603b38759f5b78891bcabd5b5e78e89207d5f3603246e2fd2deaae04a058e (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=054603b38759f5b78891bcabd5b5e78e89207d5f3603246e2fd2deaae04a058e)
2025-06-20 07:21:11,917 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c9266f09e304d12e2a1ffd988658ac8510e6a55a14e4981ce8bdbaab7c2fafb1
2025-06-20 07:21:11,925 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c9266f09e304d12e2a1ffd988658ac8510e6a55a14e4981ce8bdbaab7c2fafb1 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c9266f09e304d12e2a1ffd988658ac8510e6a55a14e4981ce8bdbaab7c2fafb1)
2025-06-20 13:51:34,337 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d047ca960030ba210fda10bf9435792c4f2285e640fca3146b31acb483a41ec
2025-06-20 13:51:34,343 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d047ca960030ba210fda10bf9435792c4f2285e640fca3146b31acb483a41ec (Caused by: RequestTimeout: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=4d047ca960030ba210fda10bf9435792c4f2285e640fca3146b31acb483a41ec)
2025-06-20 13:51:35,200 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 71, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 188, in sync_wrapper
    time.sleep(sleep_time)

2025-06-21 14:39:56,679 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=450ecfb1d2889141dad694f69bb8d7b6377b2e256bf2f97596d4713f294a7907
2025-06-21 14:39:56,682 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=450ecfb1d2889141dad694f69bb8d7b6377b2e256bf2f97596d4713f294a7907 (Caused by: RequestTimeout: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=450ecfb1d2889141dad694f69bb8d7b6377b2e256bf2f97596d4713f294a7907)
2025-06-21 14:39:59,160 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 71, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 57, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 449, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 493, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
  File "/usr/lib/python3.10/ssl.py", line 513, in wrap_socket
    return self.sslsocket_class._create(
  File "/usr/lib/python3.10/ssl.py", line 1100, in _create
    self.do_handshake()
  File "/usr/lib/python3.10/ssl.py", line 1371, in do_handshake
    self._sslobj.do_handshake()

2025-06-21 21:17:45,575 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c1cac8dfc749089241582d4706e746a50ed7f14b2995ef73494ea9a558d6b61f
2025-06-21 21:17:45,578 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c1cac8dfc749089241582d4706e746a50ed7f14b2995ef73494ea9a558d6b61f (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c1cac8dfc749089241582d4706e746a50ed7f14b2995ef73494ea9a558d6b61f)
2025-06-21 22:24:35,216 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6cddfcc5f6a54076b9db1ce106fd264c5a831b1516db95abf1adb74028427b00
2025-06-21 22:24:35,218 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6cddfcc5f6a54076b9db1ce106fd264c5a831b1516db95abf1adb74028427b00 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6cddfcc5f6a54076b9db1ce106fd264c5a831b1516db95abf1adb74028427b00)
2025-06-22 02:33:06,654 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=562b11e93c7f3c58c2320ab55ea69592254b5f4fe21cd3514828a89ef7e0f6dd
2025-06-22 02:33:06,657 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=562b11e93c7f3c58c2320ab55ea69592254b5f4fe21cd3514828a89ef7e0f6dd (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=562b11e93c7f3c58c2320ab55ea69592254b5f4fe21cd3514828a89ef7e0f6dd)
2025-06-22 02:42:31,553 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=fd4f52740a2efd4904b5169a7ab0d9c63824a8b57d83e4233a87b4605824d73b
2025-06-22 02:42:31,555 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=fd4f52740a2efd4904b5169a7ab0d9c63824a8b57d83e4233a87b4605824d73b (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=fd4f52740a2efd4904b5169a7ab0d9c63824a8b57d83e4233a87b4605824d73b)
2025-06-22 02:43:36,101 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a21fbb6019da9d10d17d01b236810b4a64928aac211f69e5c839fc8aaa712978
2025-06-22 02:43:36,103 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a21fbb6019da9d10d17d01b236810b4a64928aac211f69e5c839fc8aaa712978 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a21fbb6019da9d10d17d01b236810b4a64928aac211f69e5c839fc8aaa712978)
2025-06-22 03:33:43,007 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7dc8c79a7f239ef72956cc89ffa423bf2004d29a864f8a23721a199abd18bdfb
2025-06-22 03:33:43,012 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7dc8c79a7f239ef72956cc89ffa423bf2004d29a864f8a23721a199abd18bdfb (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7dc8c79a7f239ef72956cc89ffa423bf2004d29a864f8a23721a199abd18bdfb)
2025-06-22 04:01:53,261 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=725ba246e28298b8ecbc9a7509d80b8e18a766b112beb79a59fb1b78e4204f13
2025-06-22 04:01:53,263 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=725ba246e28298b8ecbc9a7509d80b8e18a766b112beb79a59fb1b78e4204f13 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=725ba246e28298b8ecbc9a7509d80b8e18a766b112beb79a59fb1b78e4204f13)
2025-06-22 06:12:19,390 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5595bddb59f4f5c6d61ff7576382b23320b2fdb605251139751eec4a72c349bc
2025-06-22 06:12:19,391 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5595bddb59f4f5c6d61ff7576382b23320b2fdb605251139751eec4a72c349bc (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5595bddb59f4f5c6d61ff7576382b23320b2fdb605251139751eec4a72c349bc)
2025-06-22 11:20:17,082 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=dcec25499a8c60a40e9ff3e979ab00aa73f6f83ac5a92d08d73a2d4619f3d5f9
2025-06-22 11:20:17,086 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=dcec25499a8c60a40e9ff3e979ab00aa73f6f83ac5a92d08d73a2d4619f3d5f9 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=dcec25499a8c60a40e9ff3e979ab00aa73f6f83ac5a92d08d73a2d4619f3d5f9)
2025-06-22 11:29:44,375 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6059c434b3a3e3c390e0ffb7d3779177e49f0d947f505a2eaa007440422fb793
2025-06-22 11:29:44,378 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6059c434b3a3e3c390e0ffb7d3779177e49f0d947f505a2eaa007440422fb793 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6059c434b3a3e3c390e0ffb7d3779177e49f0d947f505a2eaa007440422fb793)
2025-06-23 00:40:01,477 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=70a45371e3c8ddeca7ad1fbf567105526a7b00b6dc5144cc9e018e30f5d19307
2025-06-23 00:40:01,481 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=70a45371e3c8ddeca7ad1fbf567105526a7b00b6dc5144cc9e018e30f5d19307 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=70a45371e3c8ddeca7ad1fbf567105526a7b00b6dc5144cc9e018e30f5d19307)
2025-06-23 07:50:16,879 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3edc54c9e4cce79e9c909dee232213c641c141e35de9e22e46c61a48c1cd64b7
2025-06-23 07:50:16,881 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3edc54c9e4cce79e9c909dee232213c641c141e35de9e22e46c61a48c1cd64b7 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3edc54c9e4cce79e9c909dee232213c641c141e35de9e22e46c61a48c1cd64b7)
2025-06-23 08:14:20,728 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=27424ae9f0a9eb63403549bec754b396fac435466441dab43f7b29421bc3606f
2025-06-23 08:14:20,731 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=27424ae9f0a9eb63403549bec754b396fac435466441dab43f7b29421bc3606f (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=27424ae9f0a9eb63403549bec754b396fac435466441dab43f7b29421bc3606f)
2025-06-23 17:06:11,556 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 14.7s behind.
2025-06-23 17:06:11,792 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 15.0s behind.
2025-06-24 08:10:48,767 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 375, in watchlist_updater
    embed_content = format_watchlist_message(watchlist_data)
  File "/root/chartfix/utils/ui_components.py", line 370, in format_watchlist_message
    return format_price_message(prices)
  File "/root/chartfix/utils/ui_components.py", line 258, in format_price_message
    volume = fetch_volume_data(symbol)
  File "/root/chartfix/utils/ui_components.py", line 391, in fetch_volume_data
    ticker = exchange.fetch_ticker(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 746, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-24 08:10:58,772 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 375, in watchlist_updater
    embed_content = format_watchlist_message(watchlist_data)
  File "/root/chartfix/utils/ui_components.py", line 370, in format_watchlist_message
    return format_price_message(prices)
  File "/root/chartfix/utils/ui_components.py", line 258, in format_price_message
    volume = fetch_volume_data(symbol)
  File "/root/chartfix/utils/ui_components.py", line 391, in fetch_volume_data
    ticker = exchange.fetch_ticker(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 746, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-24 08:11:02,490 - utils.ui_components - ERROR - Lỗi khi lấy khối lượng giao dịch cho DOGEUSDT: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=DOGEUSDT
2025-06-24 11:15:52,347 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=211b9450c7a33b3a0d43bd99061dcf42125edd0d5db2bea5eecb3fbdc332aab7
2025-06-24 11:15:52,350 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=211b9450c7a33b3a0d43bd99061dcf42125edd0d5db2bea5eecb3fbdc332aab7 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=211b9450c7a33b3a0d43bd99061dcf42125edd0d5db2bea5eecb3fbdc332aab7)
2025-06-24 15:03:27,179 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d0073ef18b0bcf03cd93b4228cbe97880c7266ce7efeaf73bef1b7dc6e4e38d1
2025-06-24 15:03:27,181 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d0073ef18b0bcf03cd93b4228cbe97880c7266ce7efeaf73bef1b7dc6e4e38d1 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d0073ef18b0bcf03cd93b4228cbe97880c7266ce7efeaf73bef1b7dc6e4e38d1)
2025-06-24 15:10:49,397 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b6bacd1c1d25a0bdfe351227c9fa4f6ad7000d23a3f7cf54ddb723542d5bb698
2025-06-24 15:10:49,402 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b6bacd1c1d25a0bdfe351227c9fa4f6ad7000d23a3f7cf54ddb723542d5bb698 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b6bacd1c1d25a0bdfe351227c9fa4f6ad7000d23a3f7cf54ddb723542d5bb698)
2025-06-24 15:20:16,526 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=584d5944bc8ff12222add6808cddbfa6be21e49d4ea83a9a112d86d86744f4a4
2025-06-24 15:20:16,532 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=584d5944bc8ff12222add6808cddbfa6be21e49d4ea83a9a112d86d86744f4a4 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=584d5944bc8ff12222add6808cddbfa6be21e49d4ea83a9a112d86d86744f4a4)
2025-06-24 19:00:01,027 - services.market.market_service - ERROR - Error fetching OHLCV data for ETHUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1d&limit=2&symbol=ETHUSDT 503 Service Temporarily Unavailable <!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html>
<head><title>503 Service Temporarily Unavailable</title></head>
<body>
<center><h1>503 Service Temporarily Unavailable</h1></center>
 Sorry for the inconvenience.<br/>
Please report this message and include the following information to us.<br/>
Thank you very much!</p>
<table>
<tr>
<td>URL:</td>
<td>http://fapi.binance.com/fapi/v1/klines?interval=1d&amp;limit=2&amp;symbol=ETHUSDT</td>
</tr>
<tr>
<td>Server:</td>
<td>ip-10-118-198-182.ap-northeast-1.compute.internal</td>
</tr>
<tr>
<td>Date:</td>
<td>2025/06/24 19:00:00</td>
</tr>
</table>
<hr/>Powered by Tengine<hr><center>tengine</center>
</body>
</html>
2025-06-24 19:00:01,036 - utils.ui_components - WARNING - Không thể lấy giá mở cửa ngày cho ETHUSDT
2025-06-26 00:56:10,161 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 71, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 57, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-26 00:56:20,162 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 76, in update_all_data
    positions_result = self.trading_service.get_positions()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 333, in get_positions
    positions = self.exchange.fetch_positions()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10139, in fetch_positions
    return self.fetch_positions_risk(symbols, params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10309, in fetch_positions_risk
    response = self.fapiPrivateV3GetPositionRisk(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-26 00:56:30,164 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 76, in update_all_data
    positions_result = self.trading_service.get_positions()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 333, in get_positions
    positions = self.exchange.fetch_positions()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10139, in fetch_positions
    return self.fetch_positions_risk(symbols, params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10309, in fetch_positions_risk
    response = self.fapiPrivateV3GetPositionRisk(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-26 00:56:40,165 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 601, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 55, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 99, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 300, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-06-26 03:38:10,708 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ddbb0c874a3ba04f586be672bcc00da087c14661b5ee021e186e704d83f5ab60
2025-06-26 03:38:10,711 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ddbb0c874a3ba04f586be672bcc00da087c14661b5ee021e186e704d83f5ab60 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ddbb0c874a3ba04f586be672bcc00da087c14661b5ee021e186e704d83f5ab60)
2025-06-26 05:24:33,148 - services.market.market_service - ERROR - Error fetching OHLCV data for BNBUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1d&limit=2&symbol=BNBUSDT
2025-06-26 11:30:33,978 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:31:37,444 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:32:40,013 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:33:42,536 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:34:45,086 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:35:47,636 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:36:50,199 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:37:52,778 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:38:55,371 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:39:57,903 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:41:00,646 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:42:03,185 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:43:05,778 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:44:08,303 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:45:10,936 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:46:13,425 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:47:15,957 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:48:18,492 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:49:21,011 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:50:23,525 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:51:26,095 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:52:28,602 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:53:28,617 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=362b8345ebdbe991edcc84b42d562f4d9cfad923f086669338eb88e35689d963
2025-06-26 11:53:28,620 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=362b8345ebdbe991edcc84b42d562f4d9cfad923f086669338eb88e35689d963 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=362b8345ebdbe991edcc84b42d562f4d9cfad923f086669338eb88e35689d963)
2025-06-26 11:53:33,314 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:54:35,876 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:55:38,406 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:56:41,021 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:57:43,604 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:58:46,121 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 11:59:48,685 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:00:51,219 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:01:53,806 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:02:56,352 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:03:58,883 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:05:01,489 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:06:06,565 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:07:09,131 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:08:11,697 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:09:14,243 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:11:19,374 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:12:21,942 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:13:24,502 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:14:27,012 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:15:29,653 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:16:33,176 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:17:35,750 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:18:38,385 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:19:41,014 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:20:43,527 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:21:45,870 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:22:48,096 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:23:50,314 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:24:52,534 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:25:54,766 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:26:56,994 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:27:59,233 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:29:01,443 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:30:06,638 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:31:08,865 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:32:11,093 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:33:13,316 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:34:15,541 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:35:17,768 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:36:20,023 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:37:22,263 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:38:24,492 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:39:26,726 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:40:28,963 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:41:31,189 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:42:33,409 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:43:37,011 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:44:39,237 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:45:41,467 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:46:43,683 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:47:45,903 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:48:48,141 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:49:50,365 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:50:52,613 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:51:54,837 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:52:57,062 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:53:59,307 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:55:01,527 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:56:03,757 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:57:06,517 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:58:08,748 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 12:59:10,979 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:00:13,222 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:01:15,472 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:02:17,685 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:03:19,902 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:04:22,128 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:05:24,352 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:06:26,577 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:07:28,802 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:08:31,025 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:09:33,245 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:10:36,347 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:11:38,583 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:12:40,819 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:13:43,042 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:14:45,260 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:15:47,492 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:16:49,706 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:17:51,924 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:18:54,136 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:19:56,352 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:20:58,561 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:22:00,819 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:23:03,054 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:24:06,923 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:25:06,768 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:26:09,439 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:27:18,597 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:27:23,048 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-26 13:27:23,666 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-06-26 13:45:56,843 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:46:14,662 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 10 messages from #📝-bot-logs (keep_pinned: True)
2025-06-26 13:46:31,215 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #📈-market-data (keep_pinned: True)
2025-06-26 13:46:59,373 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:47:09,736 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376629175934390393/messages/1381212068568633385 responded with 429. Retrying in 0.54 seconds.
2025-06-26 13:47:10,954 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 8 messages from #trade (keep_pinned: True)
2025-06-26 13:47:14,563 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383760111931228262 responded with 429. Retrying in 0.41 seconds.
2025-06-26 13:47:15,594 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383757573928390738 responded with 429. Retrying in 0.38 seconds.
2025-06-26 13:47:16,776 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383755057492660345 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:17,696 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383697176005447790 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:18,758 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383687109356814486 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:20,849 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383599029950349385 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:21,790 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383599028427558972 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:22,876 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383597768290996236 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:24,429 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383490817943015626 responded with 429. Retrying in 0.54 seconds.
2025-06-26 13:47:25,650 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383480764074823892 responded with 429. Retrying in 0.32 seconds.
2025-06-26 13:47:26,650 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383473219125645473 responded with 429. Retrying in 0.32 seconds.
2025-06-26 13:47:27,653 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383468166327570444 responded with 429. Retrying in 0.32 seconds.
2025-06-26 13:47:28,612 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383435464618541149 responded with 429. Retrying in 0.38 seconds.
2025-06-26 13:47:29,709 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383430417981636668 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:30,634 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383407768551034920 responded with 429. Retrying in 0.34 seconds.
2025-06-26 13:47:31,650 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383314657477132402 responded with 429. Retrying in 0.32 seconds.
2025-06-26 13:47:32,606 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383269371383775342 responded with 429. Retrying in 0.37 seconds.
2025-06-26 13:47:33,659 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383268099171680378 responded with 429. Retrying in 0.32 seconds.
2025-06-26 13:47:34,595 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383265593267982497 responded with 429. Retrying in 0.38 seconds.
2025-06-26 13:47:35,240 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383265593267982497 responded with 429. Retrying in 0.73 seconds.
2025-06-26 13:47:35,518 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387791013359386776 responded with 429. Retrying in 0.45 seconds.
2025-06-26 13:47:36,284 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387791013359386776 responded with 429. Retrying in 0.69 seconds.
2025-06-26 13:47:36,753 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383259291363971174 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:37,346 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383259291363971174 responded with 429. Retrying in 0.64 seconds.
2025-06-26 13:47:38,606 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383256774001557574 responded with 429. Retrying in 0.36 seconds.
2025-06-26 13:47:38,654 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786325465235548 responded with 429. Retrying in 0.34 seconds.
2025-06-26 13:47:39,302 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786325465235548 responded with 429. Retrying in 0.67 seconds.
2025-06-26 13:47:39,629 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383254270757044285 responded with 429. Retrying in 0.36 seconds.
2025-06-26 13:47:40,302 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383254270757044285 responded with 429. Retrying in 0.67 seconds.
2025-06-26 13:47:40,678 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786322285957331 responded with 429. Retrying in 0.31 seconds.
2025-06-26 13:47:41,291 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786322285957331 responded with 429. Retrying in 0.69 seconds.
2025-06-26 13:47:41,666 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383251741344006155 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:42,305 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786322285957331 responded with 429. Retrying in 0.68 seconds.
2025-06-26 13:47:42,600 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383244191160602734 responded with 429. Retrying in 0.39 seconds.
2025-06-26 13:47:43,312 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786322285957331 responded with 429. Retrying in 0.67 seconds.
2025-06-26 13:47:43,670 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383241673399861310 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:44,284 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786322285957331 responded with 429. Retrying in 0.69 seconds.
2025-06-26 13:47:44,688 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383239173477105674 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:45,637 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786036855439483 responded with 429. Retrying in 0.33 seconds.
2025-06-26 13:47:45,659 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383239172029939722 responded with 429. Retrying in 0.33 seconds.
2025-06-26 13:47:46,295 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383239172029939722 responded with 429. Retrying in 0.69 seconds.
2025-06-26 13:47:46,635 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387786032790900798 responded with 429. Retrying in 0.34 seconds.
2025-06-26 13:47:47,422 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383239172029939722 responded with 429. Retrying in 0.55 seconds.
2025-06-26 13:47:47,722 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387785768491155487 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:48,345 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383239172029939722 responded with 429. Retrying in 0.63 seconds.
2025-06-26 13:47:48,688 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387785765018140685 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:47:49,275 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387785765018140685 responded with 429. Retrying in 0.70 seconds.
2025-06-26 13:47:49,573 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383237899285172256 responded with 429. Retrying in 0.40 seconds.
2025-06-26 13:47:50,306 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383237899285172256 responded with 429. Retrying in 0.67 seconds.
2025-06-26 13:47:51,625 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1384862588818690128 responded with 429. Retrying in 0.34 seconds.
2025-06-26 13:47:51,635 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383235390051192852 responded with 429. Retrying in 0.34 seconds.
2025-06-26 13:47:52,280 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1384862588818690128 responded with 429. Retrying in 0.71 seconds.
2025-06-26 13:47:52,612 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383231621062459472 responded with 429. Retrying in 0.36 seconds.
2025-06-26 13:47:53,272 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1384862588818690128 responded with 429. Retrying in 0.70 seconds.
2025-06-26 13:47:53,557 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383138495434068130 responded with 429. Retrying in 0.41 seconds.
2025-06-26 13:47:54,249 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383138495434068130 responded with 429. Retrying in 0.72 seconds.
2025-06-26 13:47:54,563 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1384862586146914468 responded with 429. Retrying in 0.41 seconds.
2025-06-26 13:47:54,711 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 6 messages from #trade (keep_pinned: False)
2025-06-26 13:47:55,271 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383138495434068130 responded with 429. Retrying in 0.70 seconds.
2025-06-26 13:47:55,600 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383265593267982497 responded with 429. Retrying in 0.37 seconds.
2025-06-26 13:47:56,314 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383265593267982497 responded with 429. Retrying in 0.67 seconds.
2025-06-26 13:48:02,162 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 13:48:03,905 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382972401058582529 responded with 429. Retrying in 0.54 seconds.
2025-06-26 13:48:05,123 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382962332803530846 responded with 429. Retrying in 0.33 seconds.
2025-06-26 13:48:06,068 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 404 Not Found (error code: 10008): Unknown Message
2025-06-26 13:48:06,130 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382954782917918773 responded with 429. Retrying in 0.32 seconds.
2025-06-26 13:48:07,161 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382952281661509682 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:48:08,261 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382952279539318878 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:48:08,437 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 2/3): 404 Not Found (error code: 10008): Unknown Message
2025-06-26 13:48:09,261 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1383251741344006155 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:48:09,263 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382949758435332106 responded with 429. Retrying in 0.30 seconds.
2025-06-26 13:48:09,855 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1382949758435332106 responded with 429. Retrying in 0.59 seconds.
2025-06-26 14:09:13,524 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:10:16,168 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:11:18,704 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:12:21,321 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:13:23,854 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:14:26,397 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:15:29,015 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:16:31,542 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:17:34,135 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:18:36,661 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:19:20,672 - handlers.discord.market.watchlist_commands - ERROR - Error in watchlist command: type object 'datetime.time' has no attribute 'time'
2025-06-26 14:19:21,057 - discord.app_commands.tree - ERROR - Ignoring exception in command 'watchlist'
Traceback (most recent call last):
  File "/root/chartfix/handlers/discord/market/watchlist_commands.py", line 64, in watchlist
    await self.bot.record_command_execution("watchlist", start_time, True)
  File "/root/chartfix/bot.py", line 133, in record_command_execution
    response_time = time.time() - start_time
AttributeError: type object 'datetime.time' has no attribute 'time'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/app_commands/commands.py", line 857, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
  File "/root/chartfix/handlers/discord/market/watchlist_commands.py", line 70, in watchlist
    await self.bot.record_command_execution("watchlist", start_time, False)
  File "/root/chartfix/bot.py", line 133, in record_command_execution
    response_time = time.time() - start_time
AttributeError: type object 'datetime.time' has no attribute 'time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/app_commands/tree.py", line 1310, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "/usr/local/lib/python3.10/dist-packages/discord/app_commands/commands.py", line 883, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
  File "/usr/local/lib/python3.10/dist-packages/discord/app_commands/commands.py", line 876, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'watchlist' raised an exception: AttributeError: type object 'datetime.time' has no attribute 'time'
2025-06-26 14:19:39,270 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:19:51,297 - utils.ui_components - ERROR - Error formatting watchlist message: 'small_up'
2025-06-26 14:20:13,604 - handlers.discord.market.watchlist_commands - ERROR - Error in watchlist command: type object 'datetime.time' has no attribute 'time'
2025-06-26 14:20:13,967 - discord.app_commands.tree - ERROR - Ignoring exception in command 'watchlist'
Traceback (most recent call last):
  File "/root/chartfix/handlers/discord/market/watchlist_commands.py", line 64, in watchlist
    await self.bot.record_command_execution("watchlist", start_time, True)
  File "/root/chartfix/bot.py", line 133, in record_command_execution
    response_time = time.time() - start_time
AttributeError: type object 'datetime.time' has no attribute 'time'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/app_commands/commands.py", line 857, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
  File "/root/chartfix/handlers/discord/market/watchlist_commands.py", line 70, in watchlist
    await self.bot.record_command_execution("watchlist", start_time, False)
  File "/root/chartfix/bot.py", line 133, in record_command_execution
    response_time = time.time() - start_time
AttributeError: type object 'datetime.time' has no attribute 'time'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/app_commands/tree.py", line 1310, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "/usr/local/lib/python3.10/dist-packages/discord/app_commands/commands.py", line 883, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
  File "/usr/local/lib/python3.10/dist-packages/discord/app_commands/commands.py", line 876, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'watchlist' raised an exception: AttributeError: type object 'datetime.time' has no attribute 'time'
2025-06-26 14:20:41,802 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:21:22,366 - utils.ui_components - ERROR - Error formatting watchlist message: 'small_up'
2025-06-26 14:21:44,424 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:22:46,977 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:22:52,190 - utils.ui_components - ERROR - Error formatting watchlist message: 'small_up'
2025-06-26 14:23:49,626 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:24:52,126 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:25:54,737 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:26:57,367 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:27:59,938 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:29:02,564 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:30:05,196 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:31:07,726 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:32:10,364 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:33:12,924 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:34:15,531 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:35:18,148 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:36:20,746 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:37:23,319 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:38:25,852 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:39:28,615 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:40:31,128 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:41:33,725 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:42:36,322 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:43:38,896 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:44:41,471 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:45:44,040 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:46:46,613 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:47:49,276 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:48:51,806 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:49:54,344 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:50:56,885 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:51:59,418 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:53:02,058 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:54:04,686 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:55:07,343 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:56:09,965 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:57:12,549 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:58:15,121 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 14:59:17,978 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:00:20,551 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:01:23,174 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:02:25,861 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:03:28,410 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:04:30,918 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:05:33,409 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:06:35,949 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:07:38,553 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:08:41,112 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:09:43,648 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:10:46,219 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:11:48,765 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:12:51,268 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:13:53,810 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:14:56,381 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:15:58,945 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:17:01,518 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:18:04,160 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:19:06,699 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:20:09,232 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:21:11,825 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:22:14,420 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:23:16,954 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:24:19,456 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:25:21,994 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:26:24,521 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:27:27,063 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:28:29,579 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:29:32,090 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:30:34,682 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:31:37,338 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:32:39,877 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:33:42,430 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:34:45,027 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:35:47,600 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:36:50,153 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:37:52,805 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:38:55,303 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:39:57,858 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:41:00,257 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:42:02,572 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:43:04,805 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:44:07,030 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:45:09,268 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:46:11,509 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:47:13,732 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:48:15,949 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:49:18,190 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:50:20,405 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:51:22,639 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:52:24,876 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:53:27,099 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:54:29,321 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:55:31,557 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:56:33,771 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:57:35,988 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:58:38,214 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 15:59:40,438 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:00:42,654 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:01:44,881 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:02:47,101 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:03:49,321 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:04:51,561 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:05:53,774 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:06:55,993 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:07:58,214 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:09:00,438 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:10:02,666 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:11:04,898 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:12:07,125 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:13:09,393 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:14:11,622 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:15:13,874 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:16:16,100 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:17:18,324 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:18:20,567 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:19:22,810 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:20:25,027 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:21:27,314 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:22:29,530 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:23:31,739 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:24:33,952 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:25:36,193 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:26:38,434 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:27:40,650 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:28:42,863 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:29:45,070 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:30:47,297 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:31:49,513 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:32:51,735 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:33:53,948 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:34:56,168 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:35:58,403 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:37:00,622 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:38:02,850 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:39:05,086 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:40:07,304 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:41:09,543 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:42:11,775 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:43:13,997 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:44:16,206 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:45:18,425 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:46:20,643 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:47:22,868 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:48:25,096 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:49:27,314 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:50:29,534 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:51:31,743 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:52:33,956 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:53:36,168 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:54:38,403 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:55:40,633 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:56:42,844 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:57:45,053 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:58:47,268 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 16:59:49,487 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:00:51,700 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:01:53,924 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:02:56,146 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:03:58,375 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:05:00,596 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:06:02,812 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:07:05,027 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:08:07,250 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:09:09,475 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:10:11,704 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:11:13,927 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:12:16,143 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:13:18,358 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:14:20,570 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:15:22,791 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:16:25,008 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:17:27,231 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:18:29,449 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:19:31,671 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:20:33,884 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:21:36,121 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:22:38,346 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:23:40,569 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:24:42,788 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:25:45,012 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:26:47,236 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:27:49,473 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:28:51,688 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:29:53,897 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:30:56,117 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:31:58,340 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:33:00,567 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:34:02,792 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:35:05,007 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:36:07,229 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:37:09,438 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:38:11,651 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:39:13,868 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:40:16,075 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:41:18,289 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:42:20,500 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:43:22,715 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:44:24,929 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:45:27,136 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:46:29,356 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:47:31,585 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:48:33,809 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:49:36,025 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:50:38,246 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:51:40,467 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:52:42,693 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:53:44,915 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:54:47,130 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:55:49,352 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:56:51,569 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:57:53,777 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:58:55,995 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 17:59:58,217 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:01:00,440 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:02:02,661 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:03:04,900 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:04:07,114 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:05:09,335 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:06:11,553 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:07:13,770 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:08:16,008 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:09:18,222 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:10:20,436 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:11:22,654 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:12:24,880 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:13:27,108 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:14:29,328 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:15:31,545 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:16:33,764 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:17:35,992 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:18:38,218 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:19:40,443 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:20:42,658 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:21:44,881 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:22:47,092 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:23:49,303 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:24:51,530 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:25:53,747 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:26:55,958 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:27:58,169 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:29:00,395 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:30:02,626 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:31:04,861 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:32:07,083 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:33:09,311 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:34:11,540 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:35:13,758 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:36:15,977 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:37:18,215 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:38:20,437 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:39:22,647 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:40:24,870 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:41:27,093 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:42:29,605 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:43:32,090 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:44:34,656 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:45:37,180 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:46:39,680 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:47:42,246 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:48:44,786 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:49:47,306 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:50:49,805 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:51:52,282 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:52:54,797 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:53:57,289 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:54:59,777 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:56:02,296 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:57:04,933 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:58:07,471 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 18:59:09,996 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:00:12,546 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:01:15,120 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:02:17,637 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:03:20,179 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:04:22,726 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:05:25,298 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:06:27,854 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:07:30,390 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:08:33,011 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:09:35,548 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:10:38,076 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:11:40,643 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:12:43,210 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:13:45,757 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:14:48,252 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:15:50,861 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:16:53,459 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:17:56,007 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:18:58,534 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:20:01,026 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:21:03,560 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:22:06,161 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:23:08,704 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:24:11,255 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:25:13,847 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:26:16,425 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:27:18,976 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:28:21,526 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:29:24,024 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:30:26,557 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:31:29,118 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:32:31,694 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:33:34,247 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:34:36,767 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:35:39,297 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:36:41,838 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:37:44,383 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:38:46,931 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:39:49,515 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:40:52,046 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:41:54,656 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:42:57,156 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:43:59,529 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:45:01,748 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:46:03,984 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:47:06,216 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:48:08,433 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:49:10,679 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:50:12,904 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:51:15,124 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:52:17,347 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:53:19,572 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:54:21,805 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:55:24,040 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:56:26,264 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:57:28,488 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:58:30,717 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 19:59:32,950 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:00:35,176 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:01:37,413 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:02:39,629 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:03:41,847 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:04:44,062 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:05:46,268 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:06:48,483 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:07:50,690 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:08:52,913 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:09:55,116 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:10:57,336 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:11:59,555 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:13:01,768 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:14:03,978 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:15:06,189 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:16:08,393 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:17:10,614 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:18:12,825 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:19:15,035 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:20:17,239 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:21:19,455 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:22:21,674 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:23:23,887 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:24:26,093 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:25:28,316 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:26:30,531 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:27:32,734 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:28:34,951 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:29:37,162 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:30:39,357 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:31:41,570 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:32:43,787 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:33:45,994 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:34:48,214 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:35:50,442 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:36:52,666 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:37:54,881 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:38:57,100 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:39:59,312 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:41:01,532 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:42:03,759 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:43:06,006 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:44:08,223 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:45:10,459 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:46:12,683 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:47:14,912 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:48:17,130 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:49:19,358 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:50:21,569 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:51:23,793 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:52:26,013 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:53:28,238 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:54:30,469 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:55:32,704 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:56:34,933 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:57:37,167 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:58:39,396 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 20:59:41,627 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:00:43,848 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:01:46,077 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:02:48,295 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:03:50,539 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:04:52,772 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:05:55,009 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:06:57,231 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:07:59,464 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:09:01,705 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:10:03,932 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:11:06,183 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:12:08,401 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:13:10,642 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:14:12,868 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:15:15,091 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:16:17,318 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:17:19,554 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:18:21,779 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:19:24,012 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:20:26,246 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:21:28,477 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:22:30,704 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:23:32,951 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:24:35,172 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:25:37,394 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:26:39,613 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:27:41,842 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:28:44,070 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:29:46,304 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:30:48,513 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:31:50,728 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:32:52,942 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:33:55,166 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:34:57,394 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:35:59,611 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:37:01,834 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:38:04,054 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:39:06,324 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:40:08,559 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:41:10,784 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:42:13,008 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:43:15,235 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:44:17,459 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:45:19,689 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:46:21,917 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:47:24,150 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:48:26,368 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:49:28,579 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:50:30,807 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:51:33,041 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:52:35,269 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:53:37,490 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:54:39,705 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:55:41,933 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:56:44,171 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:57:46,386 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:58:48,608 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 21:59:50,833 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:00:53,061 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:01:55,288 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:02:57,494 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:03:59,709 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:05:01,928 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:06:04,156 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:07:06,387 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:08:08,616 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:09:10,836 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:10:13,047 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:11:15,272 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:12:17,482 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:13:19,709 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:14:21,924 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:15:24,148 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:16:26,361 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:17:28,584 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:18:30,807 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:19:33,027 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:20:35,245 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:21:37,462 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:22:39,677 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:23:41,892 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:24:44,119 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:25:46,336 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:26:48,570 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:27:50,801 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:28:53,057 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:29:55,293 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:30:57,533 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:31:59,748 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:33:01,999 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:34:04,233 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:35:06,481 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:36:08,702 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:37:10,931 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:38:13,155 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:39:15,373 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:40:17,584 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:41:19,802 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:42:22,025 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:43:24,248 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:44:26,455 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:45:28,990 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:46:31,493 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:47:34,005 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:48:36,597 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:49:39,120 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:50:41,635 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:51:44,158 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:52:46,686 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:53:49,246 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:54:51,817 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:55:54,361 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:56:56,872 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:57:59,418 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 22:59:01,956 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:00:04,543 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:01:07,042 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:02:09,589 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:03:12,165 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:04:14,714 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:05:17,277 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:06:19,802 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:07:22,358 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:08:24,894 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:09:27,577 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:10:30,088 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:11:32,641 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:12:35,169 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:13:37,683 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:14:40,253 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:15:42,770 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:16:45,313 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:17:47,852 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:18:50,408 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:19:52,950 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:20:55,457 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:21:58,000 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:23:00,514 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:24:03,014 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:25:05,558 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:26:08,081 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:27:10,742 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:28:13,258 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:29:15,762 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:30:18,254 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:31:20,771 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:32:23,488 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:33:26,044 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:34:28,608 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:35:31,146 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:36:33,690 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:37:36,253 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:38:38,831 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:39:41,373 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:40:43,906 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:41:46,437 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:42:49,018 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:43:51,572 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:44:54,148 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:45:56,635 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:46:59,218 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:48:01,824 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:49:04,483 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:50:07,182 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:51:09,787 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:52:12,386 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:53:14,927 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:54:17,478 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:55:19,967 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:56:22,533 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:57:25,073 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:58:27,591 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-26 23:59:30,109 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:00:32,627 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:01:35,149 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:02:37,660 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:03:40,237 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:04:42,834 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:05:45,346 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:06:47,869 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:07:50,393 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:08:52,968 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:09:55,505 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:10:58,010 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:12:00,491 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:13:03,014 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:14:05,533 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:15:08,051 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:16:10,600 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:17:13,237 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:18:15,856 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:19:18,385 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:20:20,906 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:21:23,460 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:22:26,038 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:23:28,608 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:24:31,089 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:25:33,460 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:26:35,683 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:27:37,905 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:28:40,129 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:29:42,367 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:30:44,581 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:31:46,810 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:32:49,020 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:33:51,242 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:34:53,502 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:35:55,719 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:36:57,942 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:38:00,155 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:39:02,370 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:40:04,598 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:41:06,824 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:42:09,051 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:43:11,298 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:44:13,538 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:45:15,749 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:46:17,964 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:47:20,192 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:48:22,417 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:49:24,622 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:50:26,836 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:51:29,051 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:52:31,270 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:53:33,497 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:54:35,712 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:55:37,932 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:56:40,172 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:57:42,410 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:58:44,622 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 00:59:46,841 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:00:49,078 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:01:51,349 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:02:53,573 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:03:55,802 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:04:58,033 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:06:00,259 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:07:02,476 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:08:04,697 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:09:06,929 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:10:09,163 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:11:11,390 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:12:13,615 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:13:15,838 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:14:18,066 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:15:20,282 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:16:22,514 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:17:24,730 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:18:26,951 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:19:29,181 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:20:31,394 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:21:33,622 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:22:35,842 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:23:38,073 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:24:40,301 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:25:42,561 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:26:44,782 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:27:47,005 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:28:49,234 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:29:51,490 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:30:53,712 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:31:55,938 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:32:58,163 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:34:00,392 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:35:02,614 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:36:04,831 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:37:07,066 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:38:09,285 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:39:11,513 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:40:13,733 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:41:15,954 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:42:18,179 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:43:20,403 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:44:22,638 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:45:24,857 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:46:27,101 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:47:29,330 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:48:31,562 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:49:33,780 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:50:36,017 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:51:38,263 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:52:40,490 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:53:42,720 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:54:44,951 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:55:47,190 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:56:49,410 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:57:51,636 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:58:53,890 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 01:59:56,122 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:00:58,389 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:02:00,615 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:03:02,866 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:04:05,090 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:05:07,328 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:06:09,557 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:07:11,799 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:08:14,076 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:09:16,321 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:10:18,541 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:11:20,793 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:12:23,022 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:13:25,238 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:14:27,446 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:15:29,666 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:16:31,895 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:17:34,117 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:18:36,337 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:19:38,551 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:20:40,783 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:21:42,999 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:22:45,220 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:23:47,440 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:24:49,679 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:25:51,921 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:26:54,134 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:27:56,352 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:28:58,570 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:30:00,831 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:31:03,071 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:32:05,305 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:33:07,524 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:34:09,758 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:35:11,981 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:36:14,205 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:37:16,444 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:38:18,670 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:39:20,921 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:40:23,149 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:41:25,363 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:42:27,597 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:43:29,829 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:44:32,049 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:45:34,289 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:46:36,515 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:47:38,739 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
2025-06-27 02:48:41,287 - services.trading.data_service - ERROR - ❌ Error updating trading data: float() argument must be a string or a real number, not 'NoneType'
